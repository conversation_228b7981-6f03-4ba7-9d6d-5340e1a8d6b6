#!/usr/bin/env python3
"""
Test script to verify Kraken database storage functionality.

This script tests:
1. Kraken data fetching
2. Database storage in correct location
3. Database structure validation
4. Data integrity verification

Usage:
    python test_kraken_db_storage.py
"""

import sys
import os
import sqlite3
import pandas as pd
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_kraken_data_fetch():
    """Test fetching data from Kraken API."""
    print("=" * 60)
    print("Testing Kraken Data Fetch")
    print("=" * 60)

    try:
        from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken

        print("Fetching 10 BTC/USD hourly candles from Kraken...")
        data = get_candles_from_kraken('btcusd', 'h1', 10, ecc=True)

        if data is not None and not data.empty:
            print(f"✓ Successfully fetched {len(data)} candles from Kraken")
            print(f"  Columns: {list(data.columns)}")
            print(f"  Data types: {data.dtypes.to_dict()}")
            print(f"  Date range: {data['date'].min()} to {data['date'].max()}")
            print(f"  Price range: ${data['low'].min():.2f} - ${data['high'].max():.2f}")
            print(f"  Volume range: {data['volume'].min():.2f} - {data['volume'].max():.2f}")

            # Show sample data
            print("\nSample data (first 3 rows):")
            print(data.head(3).to_string())

            return data
        else:
            print("✗ Failed to fetch data from Kraken")
            return None

    except Exception as e:
        print(f"✗ Error testing Kraken fetch: {str(e)}")
        return None


def test_kraken_db_storage():
    """Test storing Kraken data in database."""
    print("\n" + "=" * 60)
    print("Testing Kraken Database Storage")
    print("=" * 60)

    try:
        # First fetch some data
        from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken
        from app.db.sqlite.store_in_db import store_in_db

        # Test multiple currency pairs
        test_pairs = ['btcusd', 'ethusd', 'ethbtc']
        success_count = 0

        for currency_pair in test_pairs:
            print(f"\nTesting {currency_pair.upper()}...")
            print("Fetching data for storage test...")
            data = get_candles_from_kraken(currency_pair, 'h1', 5, ecc=True)

            if data is None or data.empty:
                print(f"✗ No data to store for {currency_pair}")
                continue

            print(f"Got {len(data)} candles to store")

            # Store in database
            print("Storing data in Kraken database...")
            db_name = f"{currency_pair}.db"
            store_in_db(db_name, 'h1', data, 'kraken')

            # Check if database was created
            expected_db_path = os.path.join('app', 'db', 'data', 'kraken', db_name)

            if os.path.exists(expected_db_path):
                print(f"✓ Database created at: {expected_db_path}")
                success_count += 1
            else:
                print(f"✗ Database not created for {currency_pair}")

        print(f"\nDatabase creation results: {success_count}/{len(test_pairs)} pairs successful")
        return success_count > 0

    except Exception as e:
        print(f"✗ Error testing database storage: {str(e)}")
        return False


def test_multiple_timeframes():
    """Test storing multiple timeframes for Kraken."""
    print("\n" + "=" * 60)
    print("Testing Multiple Timeframes Storage")
    print("=" * 60)

    try:
        from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken
        from app.db.sqlite.store_in_db import store_in_db

        timeframes = ['h1', 'd1']  # Test with hourly and daily

        for timeframe in timeframes:
            print(f"\nTesting {timeframe} timeframe...")

            # Fetch data
            data = get_candles_from_kraken('btcusd', timeframe, 3, ecc=True)

            if data is not None and not data.empty:
                print(f"  ✓ Fetched {len(data)} {timeframe} candles")

                # Store data
                store_in_db('btcusd.db', timeframe, data, 'kraken')

                # Verify storage
                db_path = os.path.join('app', 'db', 'data', 'kraken', 'btcusd.db')

                if os.path.exists(db_path):
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # Check if table exists
                    cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{timeframe}'")
                    table_exists = cursor.fetchone()

                    if table_exists:
                        cursor.execute(f"SELECT COUNT(*) FROM {timeframe}")
                        count = cursor.fetchone()[0]
                        print(f"  ✓ {timeframe} table created with {count} records")
                    else:
                        print(f"  ✗ {timeframe} table not created")

                    conn.close()
                else:
                    print(f"  ✗ Database not found")
            else:
                print(f"  ✗ Failed to fetch {timeframe} data")

        return True

    except Exception as e:
        print(f"✗ Error testing multiple timeframes: {str(e)}")
        return False


def test_database_structure():
    """Test the database structure and data integrity."""
    print("\n" + "=" * 60)
    print("Testing Database Structure")
    print("=" * 60)

    try:
        db_path = os.path.join('app', 'db', 'data', 'kraken', 'btcusd.db')

        if not os.path.exists(db_path):
            print("✗ Database doesn't exist - run storage test first")
            return False

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]

        print(f"✓ Found tables: {tables}")

        for table in tables:
            print(f"\nAnalyzing table '{table}':")

            # Get table info
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()

            print(f"  Columns ({len(columns)}):")
            for col in columns:
                print(f"    {col[1]} ({col[2]}) - {'NOT NULL' if col[3] else 'NULL'}")

            # Get record count
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"  Records: {count}")

            if count > 0:
                # Get date range
                cursor.execute(f"SELECT MIN(timestamp), MAX(timestamp) FROM {table}")
                min_ts, max_ts = cursor.fetchone()

                if min_ts and max_ts:
                    min_date = datetime.fromtimestamp(min_ts)
                    max_date = datetime.fromtimestamp(max_ts)
                    print(f"  Date range: {min_date} to {max_date}")

                # Check for duplicates
                cursor.execute(f"SELECT timestamp, COUNT(*) FROM {table} GROUP BY timestamp HAVING COUNT(*) > 1")
                duplicates = cursor.fetchall()

                if duplicates:
                    print(f"  ⚠ Found {len(duplicates)} duplicate timestamps")
                else:
                    print(f"  ✓ No duplicate timestamps")

                # Check data quality
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE open <= 0 OR high <= 0 OR low <= 0 OR close <= 0")
                invalid_prices = cursor.fetchone()[0]

                if invalid_prices > 0:
                    print(f"  ⚠ Found {invalid_prices} records with invalid prices")
                else:
                    print(f"  ✓ All prices are valid")

                # Check OHLC relationships
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE high < low OR high < open OR high < close OR low > open OR low > close")
                invalid_ohlc = cursor.fetchone()[0]

                if invalid_ohlc > 0:
                    print(f"  ⚠ Found {invalid_ohlc} records with invalid OHLC relationships")
                else:
                    print(f"  ✓ All OHLC relationships are valid")

        conn.close()
        return True

    except Exception as e:
        print(f"✗ Error analyzing database structure: {str(e)}")
        return False


def test_data_retrieval():
    """Test retrieving data from Kraken database."""
    print("\n" + "=" * 60)
    print("Testing Data Retrieval")
    print("=" * 60)

    try:
        from app.db.sqlite.get_from_db import get_from_db

        print("Testing get_from_db function...")

        # Test retrieving data
        data = get_from_db('btcusd', 'h1', 5, 'kraken')

        if data is not None and not data.empty:
            print(f"✓ Successfully retrieved {len(data)} records")
            print(f"  Columns: {list(data.columns)}")
            print(f"  Data types: {data.dtypes.to_dict()}")

            # Check if data is sorted correctly (descending by timestamp)
            timestamps = data['timestamp'].tolist()
            is_sorted = all(timestamps[i] >= timestamps[i+1] for i in range(len(timestamps)-1))

            if is_sorted:
                print("  ✓ Data is correctly sorted (descending by timestamp)")
            else:
                print("  ⚠ Data is not properly sorted")

            print("\nSample retrieved data:")
            print(data.head(3).to_string())

            return True
        else:
            print("✗ Failed to retrieve data")
            return False

    except Exception as e:
        print(f"✗ Error testing data retrieval: {str(e)}")
        return False


def cleanup_test_data():
    """Clean up test databases."""
    print("\n" + "=" * 60)
    print("Cleanup")
    print("=" * 60)

    try:
        db_path = os.path.join('app', 'db', 'data', 'kraken', 'btcusd.db')

        if os.path.exists(db_path):
            # Don't actually delete - just report
            print(f"Test database exists at: {db_path}")
            print("Database preserved for inspection")
        else:
            print("No test database to clean up")

    except Exception as e:
        print(f"Error during cleanup: {str(e)}")


def main():
    """Run all Kraken database tests."""
    print("Testing Kraken Database Storage System")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")

    tests = [
        ("Kraken Data Fetch", test_kraken_data_fetch),
        ("Kraken DB Storage", test_kraken_db_storage),
        ("Multiple Timeframes", test_multiple_timeframes),
        ("Database Structure", test_database_structure),
        ("Data Retrieval", test_data_retrieval)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            if test_name == "Kraken Data Fetch":
                result = test_func()
                results.append((test_name, result is not None))
            else:
                result = test_func()
                results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))

    # Cleanup
    cleanup_test_data()

    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All Kraken database tests passed!")
    else:
        print("⚠ Some tests failed. Check the implementation.")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
