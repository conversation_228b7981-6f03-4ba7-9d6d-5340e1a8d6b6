"""
Shared test fixtures and configuration for all tests.

This module provides common fixtures and setup for the dabot-ohlc test suite.
"""
import pytest
import sys
import os
from datetime import datetime, timedelta
import pandas as pd

# Add app directory to path for all tests
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture
def sample_currency_pair():
    """Standard currency pair for testing."""
    return 'btcusd'

@pytest.fixture  
def sample_timeframe():
    """Standard timeframe for testing."""
    return 'h1'

@pytest.fixture
def sample_candles_count():
    """Standard number of candles for testing."""
    return 5

@pytest.fixture
def sample_ohlc_data():
    """Sample OHLC data for testing."""
    timestamps = [1640995200, 1640998800, 1641002400, 1641006000, 1641009600]
    return pd.DataFrame({
        'timestamp': timestamps,
        'date': pd.to_datetime(timestamps, unit='s'),
        'open': [45000.0, 45100.0, 45200.0, 45300.0, 45400.0],
        'high': [45050.0, 45150.0, 45250.0, 45350.0, 45450.0],
        'low': [44950.0, 45050.0, 45150.0, 45250.0, 45350.0],
        'close': [45100.0, 45200.0, 45300.0, 45400.0, 45500.0],
        'volume': [100.0, 150.0, 120.0, 180.0, 140.0],
        'source': ['test'] * 5,
        'quality_score': [0.95] * 5
    })

@pytest.fixture
def test_start_time():
    """Test start timestamp for consistent testing."""
    return datetime.utcnow()
