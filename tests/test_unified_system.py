#!/usr/bin/env python3
"""
Test script for the unified database aggregation system.

This script tests the complete flow:
1. Kraken integration
2. Data aggregation from multiple sources
3. Unified database storage
4. Cache-first retrieval

Usage:
    python test_unified_system.py
"""

import sys
import os
import pandas as pd
from datetime import datetime

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

def test_kraken_integration():
    """Test Kraken data fetching."""
    print("=" * 60)
    print("Testing Kraken Integration")
    print("=" * 60)

    try:
        from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken

        print("Fetching 5 BTC/USD hourly candles from Kraken...")
        data = get_candles_from_kraken('btcusd', 'h1', 5, ecc=True)

        if data is not None and not data.empty:
            print(f"✓ Successfully fetched {len(data)} candles from Kraken")
            print(f"  Columns: {list(data.columns)}")
            print(f"  Date range: {data['date'].min()} to {data['date'].max()}")
            print(f"  Price range: ${data['low'].min():.2f} - ${data['high'].max():.2f}")
            return True
        else:
            print("✗ Failed to fetch data from Kraken")
            return False

    except Exception as e:
        print(f"✗ Error testing Kraken: {str(e)}")
        return False


def test_unified_database_creation():
    """Test unified database schema creation."""
    print("\n" + "=" * 60)
    print("Testing Unified Database Creation")
    print("=" * 60)

    try:
        from app.db.unified.unified_schema import create_unified_schema, validate_schema_compliance

        # Create test database
        test_db_path = os.path.join(os.getcwd(), "test_unified.db")

        print("Creating unified database schema...")
        success = create_unified_schema(test_db_path)

        if success:
            print("✓ Unified database created successfully")

            # Validate schema
            validation = validate_schema_compliance(test_db_path)
            if validation.get("valid", False):
                print("✓ Schema validation passed")

                # Clean up
                os.remove(test_db_path)
                return True
            else:
                print(f"✗ Schema validation failed: {validation.get('issues', [])}")
                return False
        else:
            print("✗ Failed to create unified database")
            return False

    except Exception as e:
        print(f"✗ Error testing unified database: {str(e)}")
        return False


def test_data_aggregation():
    """Test data aggregation from multiple sources."""
    print("\n" + "=" * 60)
    print("Testing Data Aggregation")
    print("=" * 60)

    try:
        from app.core.aggregators.candles_aggregator import CandlesAggregator

        # Create mock data from different sources
        timestamps = [1640995200, 1640998800, 1641002400]  # 3 hourly timestamps

        bitstamp_data = pd.DataFrame({
            'timestamp': timestamps,
            'date': pd.to_datetime(timestamps, unit='s'),
            'open': [45000.0, 45100.0, 45200.0],
            'high': [45050.0, 45150.0, 45250.0],
            'low': [44950.0, 45050.0, 45150.0],
            'close': [45100.0, 45200.0, 45300.0],
            'volume': [100.0, 150.0, 120.0]
        })

        kraken_data = pd.DataFrame({
            'timestamp': timestamps,
            'date': pd.to_datetime(timestamps, unit='s'),
            'open': [45010.0, 45110.0, 45210.0],
            'high': [45060.0, 45160.0, 45260.0],
            'low': [44960.0, 45060.0, 45160.0],
            'close': [45110.0, 45210.0, 45310.0],
            'volume': [80.0, 120.0, 100.0]
        })

        source_data = {
            'bitstamp': bitstamp_data,
            'kraken': kraken_data
        }

        metadata = {
            'bitstamp': {'quality_score': 0.90},
            'kraken': {'quality_score': 0.88}
        }

        print("Aggregating data from Bitstamp and Kraken...")
        aggregator = CandlesAggregator('quality_weighted')
        aggregated_df, quality_metrics = aggregator.aggregate(source_data, metadata)

        if not aggregated_df.empty:
            print(f"✓ Successfully aggregated {len(aggregated_df)} candles")
            print(f"  Quality score: {quality_metrics.get('overall_quality', 0):.3f}")
            print(f"  Sources used: {quality_metrics.get('sources_used', 0)}")
            print(f"  Aggregation method: {quality_metrics.get('aggregation_method', 'unknown')}")
            return True
        else:
            print("✗ Aggregation failed - no data returned")
            return False

    except Exception as e:
        print(f"✗ Error testing aggregation: {str(e)}")
        return False


def test_unified_coordinator():
    """Test the unified data coordinator."""
    print("\n" + "=" * 60)
    print("Testing Unified Data Coordinator")
    print("=" * 60)

    try:
        from app.core.unified_data_coordinator import UnifiedDataCoordinator

        print("Initializing unified data coordinator...")
        coordinator = UnifiedDataCoordinator()

        print("Getting cache statistics...")
        stats = coordinator.get_cache_statistics()

        if "error" not in stats:
            print("✓ Unified coordinator initialized successfully")
            print(f"  Sources configured: {stats.get('sources_configured', [])}")
            print(f"  Aggregation strategy: {stats.get('aggregation_strategy', 'unknown')}")
            return True
        else:
            print(f"✗ Coordinator error: {stats.get('error', 'unknown')}")
            return False

    except Exception as e:
        print(f"✗ Error testing coordinator: {str(e)}")
        return False


def test_complete_flow():
    """Test the complete unified data flow."""
    print("\n" + "=" * 60)
    print("Testing Complete Unified Flow")
    print("=" * 60)

    try:
        from app.core.unified_data_coordinator import unified_coordinator

        print("Testing unified candles retrieval for BTC/USD...")
        result = unified_coordinator.get_unified_candles('btcusd', 'h1', 5, ecc=True)

        if result.get("data") is not None and not result["data"].empty:
            print(f"✓ Successfully retrieved unified data")
            print(f"  Records: {len(result['data'])}")
            print(f"  Quality score: {result.get('quality_score', 0):.3f}")
            print(f"  Cache hit: {result.get('cache_hit', False)}")
            print(f"  Sources used: {result.get('sources_used', [])}")
            return True
        elif "error" in result:
            print(f"⚠ Expected behavior - no cached data yet: {result['error']}")
            return True  # This is expected for first run
        else:
            print("✗ Unexpected result from unified coordinator")
            return False

    except Exception as e:
        print(f"✗ Error testing complete flow: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("Testing Unified Database Aggregation System")
    print("=" * 60)
    print(f"Test started at: {datetime.now()}")

    tests = [
        ("Kraken Integration", test_kraken_integration),
        ("Unified Database Creation", test_unified_database_creation),
        ("Data Aggregation", test_data_aggregation),
        ("Unified Coordinator", test_unified_coordinator),
        ("Complete Flow", test_complete_flow)
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {str(e)}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"{status:<8} {test_name}")
        if result:
            passed += 1

    print(f"\nResults: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Unified system is ready.")
    else:
        print("⚠ Some tests failed. Check the implementation.")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
