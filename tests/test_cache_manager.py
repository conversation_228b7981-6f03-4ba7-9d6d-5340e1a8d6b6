#!/usr/bin/env python3
"""
Test script for Cache Manager functionality
"""

import sys
import os
import asyncio
import pandas as pd
from datetime import datetime, timedelta

# Add app directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.core.cache_manager import CacheManager, CacheStrategy
from app.sources.kraken.get_candles_from_kraken import get_candles_from_kraken
from app.core.translators.kraken_translator import KrakenTranslator

async def test_cache_manager():
    """Test the cache manager functionality"""
    print("Testing Cache Manager...")
    
    # Initialize cache manager
    cache_manager = CacheManager(max_memory_entries=100)
    
    # Step 1: Test cache miss (no data in cache)
    print("\n1. Testing cache miss...")
    result = await cache_manager.get_cached_data('btcusd', 'h1', 5, CacheStrategy.CACHE_FIRST)
    
    print(f"Cache hit: {result['cache_hit']}")
    print(f"Cache source: {result['cache_source']}")
    print(f"Needs refresh: {result['needs_refresh']}")
    
    if not result['cache_hit']:
        print("✅ Cache miss working correctly")
    else:
        print("❌ Expected cache miss but got cache hit")
        return False
    
    # Step 2: Fetch real data and cache it
    print("\n2. Fetching and caching real data...")
    raw_data = get_candles_from_kraken('btcusd', 'h1', 5)
    
    if raw_data is None or raw_data.empty:
        print("❌ Failed to fetch data from Kraken")
        return False
    
    # Translate the data
    translator = KrakenTranslator()
    translated_data = translator.translate(raw_data)
    
    if translated_data.empty:
        print("❌ Failed to translate data")
        return False
    
    # Cache the data
    cache_success = await cache_manager.cache_data('btcusd', 'h1', translated_data, 0.95)
    
    if cache_success:
        print("✅ Successfully cached data")
    else:
        print("❌ Failed to cache data")
        return False
    
    # Step 3: Test cache hit (data should be in memory cache now)
    print("\n3. Testing cache hit...")
    result = await cache_manager.get_cached_data('btcusd', 'h1', 5, CacheStrategy.CACHE_FIRST)
    
    print(f"Cache hit: {result['cache_hit']}")
    print(f"Cache source: {result['cache_source']}")
    print(f"Freshness score: {result['freshness_score']:.3f}")
    print(f"Quality score: {result['quality_score']:.3f}")
    print(f"Data shape: {result['data'].shape if result['data'] is not None else 'None'}")
    
    if result['cache_hit'] and result['cache_source'] == 'memory':
        print("✅ Memory cache hit working correctly")
    else:
        print("❌ Expected memory cache hit")
        return False
    
    # Step 4: Test cache statistics
    print("\n4. Testing cache statistics...")
    stats = cache_manager.get_cache_statistics()
    
    print("Cache Statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    if stats['cache_hits'] > 0 and stats['hit_ratio'] > 0:
        print("✅ Cache statistics working correctly")
    else:
        print("❌ Cache statistics not working")
        return False
    
    # Step 5: Test data freshness
    print("\n5. Testing data freshness...")
    now = datetime.utcnow()
    
    # Fresh data (just created)
    is_fresh, freshness_score = cache_manager.is_data_fresh('btcusd', 'h1', now)
    print(f"Fresh data - is_fresh: {is_fresh}, score: {freshness_score:.3f}")
    
    # Stale data (2 hours old)
    old_time = now - timedelta(hours=2)
    is_fresh_old, freshness_score_old = cache_manager.is_data_fresh('btcusd', 'h1', old_time)
    print(f"Stale data - is_fresh: {is_fresh_old}, score: {freshness_score_old:.3f}")
    
    if is_fresh and not is_fresh_old:
        print("✅ Data freshness checking working correctly")
    else:
        print("❌ Data freshness checking not working")
        return False
    
    # Step 6: Test cache with different parameters
    print("\n6. Testing cache with different parameters...")
    
    # Cache data for different timeframe
    cache_success_2 = await cache_manager.cache_data('btcusd', 'm5', translated_data, 0.85)
    
    # Test retrieval
    result_2 = await cache_manager.get_cached_data('btcusd', 'm5', 5, CacheStrategy.CACHE_FIRST)
    
    if cache_success_2 and result_2['cache_hit']:
        print("✅ Multiple cache entries working correctly")
    else:
        print("❌ Multiple cache entries not working")
        return False
    
    # Step 7: Final statistics
    print("\n7. Final cache statistics...")
    final_stats = cache_manager.get_cache_statistics()
    
    print("Final Cache Statistics:")
    for key, value in final_stats.items():
        print(f"  {key}: {value}")
    
    expected_hits = 2  # We should have 2 cache hits
    if final_stats['cache_hits'] >= expected_hits:
        print("✅ Cache manager test completed successfully!")
        return True
    else:
        print(f"❌ Expected at least {expected_hits} cache hits, got {final_stats['cache_hits']}")
        return False

async def main():
    """Main test function"""
    try:
        success = await test_cache_manager()
        return success
    except Exception as e:
        print(f"❌ Test failed with exception: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
