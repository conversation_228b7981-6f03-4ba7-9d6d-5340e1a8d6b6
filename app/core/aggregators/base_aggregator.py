"""
Base Aggregator - Abstract base class for all data aggregators

This module defines the interface and common functionality for all data aggregators
in the dabot-ohlc system. It provides a consistent API for combining data from
multiple sources into unified datasets.

Key Responsibilities:
1. Define the standard interface for all aggregators
2. Provide common utility methods for data processing
3. Implement base quality assessment functionality
4. Handle common error scenarios
5. Provide logging and monitoring capabilities

Design Patterns:
- Template Method: Define algorithm structure, let subclasses implement details
- Strategy Pattern: Allow different aggregation strategies
- Observer Pattern: Notify about quality issues

Quality Assessment Framework:
- Completeness: Check for missing data points
- Consistency: Compare values across sources
- Timeliness: Validate data freshness
- Reasonableness: Detect obvious outliers

Error Handling:
- Graceful degradation when sources provide poor data
- Fallback to single source when aggregation fails
- Comprehensive error logging and reporting
"""

import pandas as pd
import numpy as np
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta

from app.logger.get_logger import log, logger


class BaseAggregator:
    """
    Abstract base class for all data aggregators.

    This class defines the common interface and provides shared functionality
    for aggregating data from multiple sources into unified datasets.
    """

    def __init__(self, aggregation_strategy: str = 'quality_weighted'):
        """
        Initialize the base aggregator.

        Args:
            aggregation_strategy: Strategy to use for aggregation
                                ('quality_weighted', 'volume_weighted',
                                 'simple_average', 'best_source')
        """
        from app.config import SOURCE_PRIORITIES, QUALITY_THRESHOLDS

        self.aggregation_strategy = aggregation_strategy
        self.source_priorities = SOURCE_PRIORITIES
        self.quality_thresholds = QUALITY_THRESHOLDS
        self.logger = logger

    def aggregate(self, source_data: Dict[str, pd.DataFrame],
                 metadata: Dict[str, Dict]) -> Tuple[pd.DataFrame, Dict]:
        """
        Main aggregation method - template method pattern.

        This method defines the overall aggregation algorithm:
        1. Validate input data
        2. Preprocess data from each source
        3. Align timestamps across sources
        4. Apply aggregation strategy
        5. Assess quality of result
        6. Post-process aggregated data

        Args:
            source_data: Dict mapping source names to DataFrames
            metadata: Dict mapping source names to metadata dicts

        Returns:
            Tuple of (aggregated_dataframe, quality_metrics)
        """
        try:
            # Step 1: Validate input data
            if not source_data:
                return pd.DataFrame(), {"error": "No source data provided"}

            valid_sources = self._validate_input_data(source_data)
            if not valid_sources:
                return pd.DataFrame(), {"error": "No valid source data"}

            # Step 2: Preprocess data from each source
            processed_data = {}
            for source, df in source_data.items():
                if source in valid_sources:
                    # For base aggregator, just use the data as-is
                    processed_data[source] = df

            if not processed_data:
                return pd.DataFrame(), {"error": "No data after preprocessing"}

            # Step 3: Align timestamps across sources
            aligned_data = self._align_timestamps(processed_data)

            # Step 4: Apply aggregation strategy
            aggregated_df = self._apply_aggregation_strategy(aligned_data)

            # Step 5: Assess quality of result
            quality_metrics = self._assess_quality(aggregated_df, source_data, metadata)

            # Step 6: Post-process aggregated data
            final_df = self._post_process_data(aggregated_df, quality_metrics)

            return final_df, quality_metrics

        except Exception as e:
            self.logger.error(f"Error in aggregation: {str(e)}")
            return pd.DataFrame(), {"error": str(e)}

    def _validate_input_data(self, source_data: Dict[str, pd.DataFrame]) -> List[str]:
        """
        Validate input data from all sources.

        Checks:
        - Data format consistency
        - Required columns presence
        - Data type validation
        - Basic sanity checks

        Args:
            source_data: Dict mapping source names to DataFrames

        Returns:
            List of valid source names
        """
        valid_sources = []
        required_columns = ['timestamp', 'open', 'high', 'low', 'close', 'volume']

        for source, df in source_data.items():
            try:
                # Check if DataFrame is not empty
                if df.empty:
                    self.logger.warning(f"Empty DataFrame from source: {source}")
                    continue

                # Check required columns
                missing_columns = [col for col in required_columns if col not in df.columns]
                if missing_columns:
                    self.logger.warning(f"Missing columns in {source}: {missing_columns}")
                    continue

                # Check data types
                if not pd.api.types.is_numeric_dtype(df['timestamp']):
                    self.logger.warning(f"Invalid timestamp type in {source}")
                    continue

                # Check for reasonable OHLC values
                numeric_cols = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_cols:
                    if not pd.api.types.is_numeric_dtype(df[col]):
                        self.logger.warning(f"Invalid {col} type in {source}")
                        break

                    if col != 'volume' and (df[col] <= 0).any():  # Prices should be positive
                        self.logger.warning(f"Non-positive {col} values in {source}")
                        break
                else:
                    # Check OHLC relationships (more lenient - only check high >= low)
                    invalid_ohlc = (df['high'] < df['low'])

                    if invalid_ohlc.any():
                        self.logger.warning(f"Invalid OHLC relationships in {source} (high < low)")
                        continue

                    valid_sources.append(source)
                    self.logger.debug(f"Validated source: {source} with {len(df)} records")

            except Exception as e:
                self.logger.error(f"Error validating {source}: {str(e)}")
                continue

        return valid_sources

    def _align_timestamps(self, source_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """Basic timestamp alignment - just return data as-is for base implementation."""
        return source_data

    def _apply_aggregation_strategy(self, aligned_data: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """Basic aggregation - just return first source for base implementation."""
        if not aligned_data:
            return pd.DataFrame()
        first_source = list(aligned_data.keys())[0]
        return aligned_data[first_source]

    def _assess_quality(self, aggregated_data: pd.DataFrame,
                       source_data: Dict[str, pd.DataFrame],
                       metadata: Dict[str, Dict] = None) -> Dict[str, Any]:
        """Basic quality assessment."""
        return {
            "overall_quality": 0.8,
            "sources_used": len(source_data),
            "aggregation_method": "basic"
        }

    def _post_process_data(self, aggregated_data: pd.DataFrame,
                          quality_metrics: Dict[str, Any]) -> pd.DataFrame:
        """Basic post-processing - just return data as-is."""
        return aggregated_data

    def _preprocess_source_data(self, source_data: Dict[str, pd.DataFrame]) -> Dict[str, pd.DataFrame]:
        """
        Preprocess data from each source before aggregation.

        Common preprocessing steps:
        - Data type conversion
        - Timestamp normalization
        - Outlier detection and handling
        - Missing value handling

        Args:
            source_data: Raw data from sources

        Returns:
            Dict of preprocessed DataFrames

        TODO: Implement preprocessing pipeline
        """
        # For now, just return the data as-is
        return source_data



    def _post_process(self, aggregated_data: pd.DataFrame) -> pd.DataFrame:
        """
        Post-process aggregated data before returning.

        Final cleanup steps:
        - Sort by timestamp
        - Remove duplicates
        - Apply final validation
        - Format for output

        Args:
            aggregated_data: Raw aggregated data

        Returns:
            Final processed DataFrame

        TODO: Implement post-processing pipeline
        """
        pass

    def _calculate_completeness(self, data: pd.DataFrame, expected_count: int) -> float:
        """
        Calculate data completeness score.

        Args:
            data: DataFrame to assess
            expected_count: Expected number of data points

        Returns:
            float: Completeness score (0-1)

        TODO: Implement completeness calculation
        """
        pass

    def _calculate_consistency(self, source_data: Dict[str, pd.DataFrame]) -> float:
        """
        Calculate consistency score across sources.

        Measures how well sources agree with each other:
        - Price agreement within tolerance
        - Volume correlation
        - Timestamp alignment

        Args:
            source_data: Data from all sources

        Returns:
            float: Consistency score (0-1)

        TODO: Implement consistency calculation
        """
        pass

    def _detect_outliers(self, data: pd.DataFrame) -> List[int]:
        """
        Detect outliers in the data.

        Uses statistical methods to identify:
        - Price outliers (beyond reasonable ranges)
        - Volume outliers (unusually high/low)
        - Timestamp gaps

        Args:
            data: DataFrame to analyze

        Returns:
            List of indices where outliers are detected

        TODO: Implement outlier detection algorithms
        """
        pass

    def get_supported_strategies(self) -> List[str]:
        """
        Get list of supported aggregation strategies.

        Returns:
            List of strategy names
        """
        pass
