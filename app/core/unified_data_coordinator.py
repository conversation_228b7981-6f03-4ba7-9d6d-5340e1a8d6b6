"""
Unified Data Coordinator - Orchestrates the complete unified data flow

This module coordinates the entire unified data aggregation process,
from fetching data from multiple sources to storing aggregated results
in the unified database.

Key Responsibilities:
1. Coordinate data fetching from all sources
2. Manage data aggregation process
3. Handle unified database operations
4. Implement cache-first strategy
5. Provide fallback mechanisms
6. Monitor data quality

Flow:
1. Check unified cache for fresh data
2. If cache miss/stale, fetch from all sources
3. Aggregate data using configured strategy
4. Store in unified database
5. Return aggregated data to endpoints
"""

import os
import json
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import pandas as pd

from app.config import (
    SOURCES, AGGREGATION_STRATEGY, UNIFIED_DB_PATH,
    CACHE_EXPIRY_MINUTES, SOURCE_PRIORITIES
)
from app.core.aggregators.candles_aggregator import CandlesAggregator
from app.db.unified.unified_schema import create_unified_schema, validate_schema_compliance
from app.db.unified.store_unified_data import store_unified_data
from app.db.unified.get_unified_data import get_unified_candles, get_unified_range
from app.logger.get_logger import log, logger


class UnifiedDataCoordinator:
    """
    Central coordinator for unified data operations.

    Manages the complete flow from source data fetching to unified storage.
    """

    def __init__(self):
        """Initialize the unified data coordinator."""
        self.sources = SOURCES
        self.aggregation_strategy = AGGREGATION_STRATEGY
        self.unified_db_path = UNIFIED_DB_PATH
        self.cache_expiry = CACHE_EXPIRY_MINUTES
        self.aggregator = CandlesAggregator(self.aggregation_strategy)

        # Ensure unified database exists
        self._ensure_unified_database()

    def _ensure_unified_database(self):
        """Ensure the unified database exists and has correct schema."""
        try:
            db_path = os.path.join(self.unified_db_path, "unified_ohlc.db")

            if not os.path.exists(db_path):
                logger.info("Creating unified database...")
                if create_unified_schema(db_path):
                    logger.info("Unified database created successfully")
                else:
                    logger.error("Failed to create unified database")
            else:
                # Validate existing schema
                validation = validate_schema_compliance(db_path)
                if not validation.get("valid", False):
                    logger.warning(f"Schema validation issues: {validation.get('issues', [])}")

        except Exception as e:
            logger.error(f"Error ensuring unified database: {str(e)}")

    def get_unified_candles(self, currency_pair: str, timeframe: str,
                           candles: int, ecc: bool = True) -> Dict[str, Any]:
        """
        Get unified candle data using cache-first approach.

        Args:
            currency_pair: Trading pair (e.g., 'btcusd')
            timeframe: Time interval (e.g., 'm1', 'h1', 'd1')
            candles: Number of candles to retrieve
            ecc: Exclude current candle

        Returns:
            Dict with unified data and metadata
        """
        try:
            # Step 1: Check unified cache
            cache_result = self._check_unified_cache(currency_pair, timeframe, candles)

            if cache_result["cache_hit"] and cache_result["is_fresh"]:
                logger.info(f"Cache hit for {currency_pair} {timeframe} {candles} candles")
                return {
                    "data": cache_result["data"],
                    "source": "unified_cache",
                    "quality_score": cache_result.get("quality_score", 0.8),
                    "cache_hit": True,
                    "aggregation_method": cache_result.get("aggregation_method", "cached")
                }

            # Step 2: Cache miss or stale data - fetch and aggregate
            logger.info(f"Cache miss/stale for {currency_pair} {timeframe} - fetching from sources")

            # Fetch from all sources
            source_data = self._fetch_from_all_sources(currency_pair, timeframe, candles, ecc)

            if not source_data:
                return {
                    "data": pd.DataFrame(),
                    "error": "No data available from any source",
                    "cache_hit": False
                }

            # Aggregate data
            aggregated_data, quality_metrics = self._aggregate_source_data(source_data)

            if aggregated_data.empty:
                return {
                    "data": pd.DataFrame(),
                    "error": "Aggregation failed",
                    "cache_hit": False,
                    "quality_metrics": quality_metrics
                }

            # Store in unified database
            self._store_unified_data(currency_pair, timeframe, aggregated_data, quality_metrics)

            return {
                "data": aggregated_data,
                "source": "aggregated",
                "quality_score": quality_metrics.get("overall_quality", 0.0),
                "cache_hit": False,
                "aggregation_method": self.aggregation_strategy,
                "sources_used": list(source_data.keys()),
                "quality_metrics": quality_metrics
            }

        except Exception as e:
            logger.error(f"Error getting unified candles: {str(e)}")
            return {
                "data": pd.DataFrame(),
                "error": str(e),
                "cache_hit": False
            }

    def _check_unified_cache(self, currency_pair: str, timeframe: str,
                           candles: int) -> Dict[str, Any]:
        """
        Check unified cache for existing data.

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            candles: Number of candles needed

        Returns:
            Dict with cache check results
        """
        try:
            # Get data from unified database
            cache_data = get_unified_candles(currency_pair, timeframe, candles)

            if cache_data.get("data") is None or cache_data["data"].empty:
                return {
                    "cache_hit": False,
                    "is_fresh": False,
                    "data": pd.DataFrame()
                }

            # Check freshness
            data_df = cache_data["data"]
            if "updated_at" in data_df.columns:
                latest_update = pd.to_datetime(data_df["updated_at"].max())
                now = datetime.utcnow()

                # Get cache expiry for this timeframe
                expiry_minutes = self.cache_expiry.get(timeframe, 60)
                expiry_time = timedelta(minutes=expiry_minutes)

                is_fresh = (now - latest_update) < expiry_time
            else:
                is_fresh = False  # No timestamp info, consider stale

            return {
                "cache_hit": True,
                "is_fresh": is_fresh,
                "data": data_df,
                "quality_score": cache_data.get("quality_score", 0.8),
                "aggregation_method": cache_data.get("aggregation_method", "cached")
            }

        except Exception as e:
            logger.error(f"Error checking unified cache: {str(e)}")
            return {
                "cache_hit": False,
                "is_fresh": False,
                "data": pd.DataFrame()
            }

    def _fetch_from_all_sources(self, currency_pair: str, timeframe: str,
                               candles: int, ecc: bool) -> Dict[str, pd.DataFrame]:
        """
        Fetch data from all available sources.

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            candles: Number of candles
            ecc: Exclude current candle

        Returns:
            Dict mapping source names to DataFrames
        """
        source_data = {}

        for source in self.sources:
            try:
                logger.debug(f"Fetching from {source}...")

                # Dynamically import and call source handler
                module_path = f"app.sources.{source}.get_candles_from_{source}"
                module = __import__(module_path, fromlist=[f"get_candles_from_{source}"])
                handler = getattr(module, f"get_candles_from_{source}")

                # Fetch data
                data = handler(currency_pair, timeframe, candles, ecc)

                if data is not None and not data.empty:
                    source_data[source] = data
                    logger.info(f"Successfully fetched {len(data)} candles from {source}")
                else:
                    logger.warning(f"No data from {source}")

            except ImportError as e:
                logger.error(f"Cannot import handler for {source}: {str(e)}")
            except Exception as e:
                logger.error(f"Error fetching from {source}: {str(e)}")

        return source_data

    def _aggregate_source_data(self, source_data: Dict[str, pd.DataFrame]) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Aggregate data from multiple sources.

        Args:
            source_data: Dict mapping source names to DataFrames

        Returns:
            Tuple of (aggregated_dataframe, quality_metrics)
        """
        try:
            if not source_data:
                return pd.DataFrame(), {"error": "No source data to aggregate"}

            # Create metadata for aggregation
            metadata = {}
            for source, df in source_data.items():
                metadata[source] = {
                    "record_count": len(df),
                    "quality_score": SOURCE_PRIORITIES.get(source, 0.5),
                    "timestamp_range": {
                        "min": int(df["timestamp"].min()) if not df.empty else 0,
                        "max": int(df["timestamp"].max()) if not df.empty else 0
                    }
                }

            # Aggregate using the configured aggregator
            aggregated_df, quality_metrics = self.aggregator.aggregate(source_data, metadata)

            return aggregated_df, quality_metrics

        except Exception as e:
            logger.error(f"Error aggregating source data: {str(e)}")
            return pd.DataFrame(), {"error": str(e)}

    def _store_unified_data(self, currency_pair: str, timeframe: str,
                           data: pd.DataFrame, quality_metrics: Dict[str, Any]):
        """
        Store aggregated data in unified database.

        Args:
            currency_pair: Trading pair
            timeframe: Time interval
            data: Aggregated DataFrame
            quality_metrics: Quality assessment results
        """
        try:
            # Prepare source metadata
            source_metadata = {
                "aggregation_timestamp": datetime.utcnow().isoformat(),
                "aggregation_method": self.aggregation_strategy,
                "sources_used": quality_metrics.get("sources_used", []),
                "quality_score": quality_metrics.get("overall_quality", 0.0)
            }

            # Store in unified database
            success = store_unified_data(
                currency_pair=currency_pair,
                timeframe=timeframe,
                aggregated_data=data,
                source_metadata=source_metadata,
                quality_metrics=quality_metrics,
                aggregation_method=self.aggregation_strategy
            )

            if success:
                logger.info(f"Successfully stored {len(data)} unified records for {currency_pair} {timeframe}")
            else:
                logger.error(f"Failed to store unified data for {currency_pair} {timeframe}")

        except Exception as e:
            logger.error(f"Error storing unified data: {str(e)}")

    def get_cache_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about the unified cache.

        Returns:
            Dict with cache statistics
        """
        try:
            # Note: For statistics, we'll need to check multiple currency pair databases
            # For now, let's check if the unified directory exists
            if not os.path.exists(self.unified_db_path):
                return {"error": "Unified database directory does not exist"}

            # Get list of unified databases (currency pairs)
            db_files = [f for f in os.listdir(self.unified_db_path) if f.endswith('.db')]
            if not db_files:
                return {"error": "No unified databases found"}

            # Use the first database for basic statistics
            db_path = os.path.join(self.unified_db_path, db_files[0])

            # Basic statistics
            import sqlite3
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Get table names (timeframes)
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name NOT LIKE '%_metadata' AND name NOT LIKE '%_metrics' AND name NOT LIKE '%_version' AND name NOT LIKE '%_log'")
            timeframes = [row[0] for row in cursor.fetchall()]

            # Get total record counts across all timeframes
            total_records = 0
            quality_scores = []

            for timeframe in timeframes:
                try:
                    cursor.execute(f"SELECT COUNT(*), AVG(quality_score) FROM {timeframe}")
                    count, avg_quality = cursor.fetchone()
                    total_records += count or 0
                    if avg_quality:
                        quality_scores.append(avg_quality)
                except:
                    continue

            # Calculate quality statistics
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)
                min_quality = min(quality_scores)
                max_quality = max(quality_scores)
            else:
                avg_quality = min_quality = max_quality = 0

            # Get currency pairs from database filenames
            currency_pairs = [f.replace('.db', '') for f in db_files]

            conn.close()

            return {
                "total_records": total_records,
                "currency_pairs": currency_pairs,
                "timeframes": timeframes,
                "quality_stats": {
                    "average": avg_quality,
                    "minimum": min_quality,
                    "maximum": max_quality
                },
                "database_files": len(db_files),
                "sources_configured": self.sources,
                "aggregation_strategy": self.aggregation_strategy
            }

        except Exception as e:
            logger.error(f"Error getting cache statistics: {str(e)}")
            return {"error": str(e)}


# Global instance for use by endpoints
unified_coordinator = UnifiedDataCoordinator()
