import pandas as pd
import pytz
from datetime import datetime
from fastapi import APIRouter
from typing import Dict, Any

from app.core.unified_data_coordinator import unified_coordinator
from app.logger.get_logger import log, logger

router = APIRouter()


@router.get("/{currency_pair}/{timeframe}/{candles}")
@log
def get_candles_endpoint(currency_pair: str, timeframe: str, candles: str) -> Dict[str, Any]:
    """
    Get OHLC candles for a given currency pair and timeframe using unified data system.

    Returns enhanced format with metadata about sources, quality, and cache status.

    Args:
        currency_pair: Trading pair (e.g., 'btc-usd')
        timeframe: Time interval (e.g., 'h1', 'd1')
        candles: Number of candles to retrieve

    Returns:
        Dict with 'data' and 'metadata' keys for success, or 'error' key for failure
    """
    try:
        # Convert candles to int
        candles_int = int(candles)

        # Get data from unified system
        result = unified_coordinator.get_unified_candles(currency_pair, timeframe, candles_int)

        # Check if we got data successfully
        if result.get("data") is not None and not result["data"].empty:
            # Convert DataFrame to list of dicts (sorted by timestamp descending)
            data_df = result["data"].sort_values(by='timestamp', ascending=False)
            candles_data = data_df.to_dict(orient='records')

            # Build enhanced response with metadata
            response = {
                "data": candles_data,
                "metadata": {
                    "cache_hit": result.get("cache_hit", False),
                    "sources_used": result.get("sources_used", []),
                    "sources_failed": result.get("sources_failed", []),
                    "quality_score": result.get("quality_metrics", {}).get("overall_quality", 0.0),
                    "aggregation_method": result.get("quality_metrics", {}).get("aggregation_method", "unknown"),
                    "total_candles": len(candles_data),
                    "currency_pair": currency_pair,
                    "timeframe": timeframe,
                    "requested_candles": candles_int
                }
            }

            logger.info(f"Successfully returned {len(candles_data)} candles for {currency_pair} {timeframe}")
            return response

        else:
            # Handle error case
            error_msg = result.get("error", "Failed to retrieve data")
            logger.error(f"Failed to get candles for {currency_pair} {timeframe}: {error_msg}")

            return {
                "error": error_msg,
                "sources_attempted": result.get("sources_attempted", []),
                "sources_failed": result.get("sources_failed", []),
                "currency_pair": currency_pair,
                "timeframe": timeframe,
                "requested_candles": candles_int
            }

    except ValueError as e:
        logger.error(f"Invalid candles parameter: {candles}")
        return {
            "error": f"Invalid candles parameter: {candles}. Must be a positive integer.",
            "currency_pair": currency_pair,
            "timeframe": timeframe
        }
    except Exception as e:
        logger.error(f"Unexpected error in get_candles_endpoint: {str(e)}")
        return {
            "error": f"Internal server error: {str(e)}",
            "currency_pair": currency_pair,
            "timeframe": timeframe
        }


